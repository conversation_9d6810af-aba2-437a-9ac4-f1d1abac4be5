/*
  print-normalize.css
  Purpose: Make print output consistent across Chrome and Firefox for print routes.
  Scope: Loaded only for print media via the print layout. Does not affect screen.
*/

@media print {
  /*
    Normalize page handling
    - Avoid engine-specific shrink-to-fit
    - Set consistent default margins
    - Do not force paper size to preserve custom label/page sizes
  */
  @page {
    size: auto; /* Let the browser/paper settings decide. Avoids unexpected scaling. */
    margin: 10mm; /* Default margin; adjust per-document with more specific rules if needed */
  }

  html, body {
    margin: 0 !important;
    padding: 0 !important;
    width: auto !important;
    height: auto !important;
    background: #fff !important;

    /* Ensure colors and backgrounds print similarly */
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    color-adjust: exact;

    /* Prevent automatic text scaling differences */
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
  }

  /*
    Your print layout wrapper (.main) in +layout@.svelte
    - Neutralize non-standard zoom differences
    - Ensure full-width rendering without max-width constraints during print
  */
  .main {
    zoom: 1 !important;           /* Reset any zoom to neutral for print */
    transform: none !important;   /* Guarantee no transforms affect print */
    max-width: none !important;
    width: 100% !important;
  }

  /* Remove visual effects that do not render well on paper */
  .shadow {
    box-shadow: none !important;
    padding: 0 !important;
    background: transparent !important;
  }

  /* Sticky elements should not be sticky on paper */
  .sticky-top,
  .position-sticky {
    position: static !important;
    top: auto !important;
  }

  /* Honor Bootstrap's print utilities across engines */
  .d-print-none { display: none !important; }

  /* Helpful utilities for page breaking control if used in content */
  .page-break { break-before: page !important; }
  .avoid-break { break-inside: avoid !important; }
  .keep-with-next { break-after: avoid !important; }

  /* Reduce orphan/widow differences between engines */
  p, h1, h2, h3, h4, h5, h6 {
    orphans: 3;
    widows: 3;
  }
}

@media screen {
  /* No screen overrides; keep runtime look unchanged */
}
